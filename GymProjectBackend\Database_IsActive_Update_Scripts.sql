-- =====================================================
-- GYM PROJECT - IsActive ALANLARINI 1'E ÇEVİREN UPDATE SCRİPTLERİ
-- =====================================================
-- Bu scriptler tüm tablolardaki IsActive alanlarını 1 (true) yapar
-- Null olan değerleri de 1 yapar
-- =====================================================

-- 1. CITIES TABLOSU
UPDATE Cities 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 2. TOWNS TABLOSU  
UPDATE Towns 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 3. COMPANIES TABLOSU
UPDATE Companies 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 4. COMPANY ADRESSES TABLOSU
UPDATE CompanyAdresses 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 5. USERS TABLOSU
UPDATE Users 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 6. COMPANY USERS TABLOSU
UPDATE CompanyUsers 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 7. USER COMPANIES TABLOSU
UPDATE UserCompanies 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 8. OPERATION CLAIMS TABLOSU
UPDATE OperationClaims 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 9. USER OPERATION CLAIMS TABLOSU
UPDATE UserOperationClaims 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 10. USER DEVICES TABLOSU
UPDATE UserDevices 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 11. MEMBERS TABLOSU
UPDATE Members 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 12. MEMBERSHIP TYPES TABLOSU
UPDATE MembershipTypes 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 13. MEMBERSHIPS TABLOSU
UPDATE Memberships 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 14. ENTRY EXIT HISTORIES TABLOSU
UPDATE EntryExitHistories 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 15. PAYMENTS TABLOSU
UPDATE Payments 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 16. PRODUCTS TABLOSU
UPDATE Products 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 17. TRANSACTIONS TABLOSU
UPDATE Transactions 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 18. DEBT PAYMENTS TABLOSU
UPDATE DebtPayments 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 19. REMAINING DEBTS TABLOSU
UPDATE RemainingDebts 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 20. EXPENSES TABLOSU
UPDATE Expenses 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 21. LICENSE PACKAGES TABLOSU
UPDATE LicensePackages 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 22. USER LICENSES TABLOSU
UPDATE UserLicenses 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 23. LICENSE TRANSACTIONS TABLOSU
UPDATE LicenseTransactions 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 24. EXERCISE CATEGORIES TABLOSU
UPDATE ExerciseCategories 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 25. SYSTEM EXERCISES TABLOSU
UPDATE SystemExercises 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 26. COMPANY EXERCISES TABLOSU
UPDATE CompanyExercises 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 27. WORKOUT PROGRAM TEMPLATES TABLOSU
UPDATE WorkoutProgramTemplates 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 28. WORKOUT PROGRAM DAYS TABLOSU
UPDATE WorkoutProgramDays 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 29. WORKOUT PROGRAM EXERCISES TABLOSU
UPDATE WorkoutProgramExercises 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 30. MEMBER WORKOUT PROGRAMS TABLOSU
UPDATE MemberWorkoutPrograms 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- 31. MEMBERSHIP FREEZE HISTORY TABLOSU
UPDATE MembershipFreezeHistory 
SET IsActive = 1 
WHERE IsActive IS NULL OR IsActive = 0;

-- =====================================================
-- KONTROL SCRİPTLERİ - İşlem sonrası kontrol için
-- =====================================================

-- Tüm tabloların IsActive durumunu kontrol et
SELECT 'Cities' as TableName, COUNT(*) as TotalRecords, 
       SUM(CASE WHEN IsActive = 1 THEN 1 ELSE 0 END) as ActiveRecords,
       SUM(CASE WHEN IsActive IS NULL THEN 1 ELSE 0 END) as NullRecords
FROM Cities
UNION ALL
SELECT 'Towns', COUNT(*), 
       SUM(CASE WHEN IsActive = 1 THEN 1 ELSE 0 END),
       SUM(CASE WHEN IsActive IS NULL THEN 1 ELSE 0 END)
FROM Towns
UNION ALL
SELECT 'Companies', COUNT(*), 
       SUM(CASE WHEN IsActive = 1 THEN 1 ELSE 0 END),
       SUM(CASE WHEN IsActive IS NULL THEN 1 ELSE 0 END)
FROM Companies
UNION ALL
SELECT 'Members', COUNT(*), 
       SUM(CASE WHEN IsActive = 1 THEN 1 ELSE 0 END),
       SUM(CASE WHEN IsActive IS NULL THEN 1 ELSE 0 END)
FROM Members
UNION ALL
SELECT 'Memberships', COUNT(*), 
       SUM(CASE WHEN IsActive = 1 THEN 1 ELSE 0 END),
       SUM(CASE WHEN IsActive IS NULL THEN 1 ELSE 0 END)
FROM Memberships;

-- =====================================================
-- NOTLAR:
-- =====================================================
-- 1. Bu scriptleri çalıştırmadan önce veritabanının yedeğini alın
-- 2. Test ortamında önce deneyin
-- 3. Scriptler tüm IsActive alanlarını 1 yapar (aktif)
-- 4. Eğer belirli kayıtların pasif kalması gerekiyorsa, 
--    o kayıtları manuel olarak 0'a çevirin
-- 5. Kontrol scriptleri ile işlem sonucunu doğrulayın
-- =====================================================
