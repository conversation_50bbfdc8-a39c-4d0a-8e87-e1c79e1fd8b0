using Core.Entities;
using System;
using System.ComponentModel.DataAnnotations;

namespace Entities.Concrete
{
    public class MemberWorkoutProgram : ICompanyEntity
    {
        [Key]
        public int MemberWorkoutProgramID { get; set; }
        public int MemberID { get; set; }
        public int WorkoutProgramTemplateID { get; set; }
        public int CompanyID { get; set; }
        public DateTime StartDate { get; set; } // Program başlangıç tarihi
        public DateTime? EndDate { get; set; } // Program bitiş tarihi (opsiyonel)
        public string? Notes { get; set; } // Atama notları
        public bool? IsActive { get; set; }
        public DateTime? CreationDate { get; set; }
        public DateTime? DeletedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
    }
}
